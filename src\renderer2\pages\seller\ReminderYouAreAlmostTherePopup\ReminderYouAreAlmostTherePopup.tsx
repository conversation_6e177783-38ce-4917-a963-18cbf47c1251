// @ts-nocheck
import { yupResolver } from "@hookform/resolvers/yup";
import { Autocomplete, Dialog, TextField } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import styles from "./ReminderYouAreAlmostTherePopup.module.scss";
import { ReactComponent as CloseIcon } from "../../../assets/New-images/New-Image-latest/Order-Management/exit-mode.svg";
import clsx from "clsx";
import InputField from "../../../component/InputField/InputField";
import { useEffect, useRef, useState } from "react";
import { CustomMenu } from "../../buyer/CustomMenu";
import * as yup from "yup";
import {
  SEND_INVOICES_TO,
  SHIPPING_DOCS_TO,
  formatPhoneNumber,
  formatPhoneNumberRemovingCountryCode,
  unformatPhoneNumber,
} from "../../../helper";
import useSaveSellerSetting from "../../../hooks/useSaveSellerSetting";
import Loader from "../../../Loader/Loader";
import { commomKeys, useGlobalStore, useSellerSettingStore } from "@bryzos/giss-ui-library";
import { useImmer } from "use-immer";
import { ReactComponent as ErrorMessageIcon } from "../../../assets/images/error-warning.svg";
import axios from "axios";
import useGetCompanyLists from "src/renderer2/hooks/useGetCompanyLists";
import useSaveUserSettings from "src/renderer2/hooks/useSaveUserSettings";
import InputWrapper from "src/renderer2/component/InputWrapper";
import CustomTextField from "src/renderer2/component/CustomTextField";
import SingleStateSelector from "../../buyer/newSettings/components/StateSelector/SingleStateSelector";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";

const schema = yup.object({
  companyAddress: yup.object().shape({
    line1: yup.string().trim().required('Line 1 is required'),
    line2: yup.string().trim().optional(),
    city: yup.string().trim().required('City is required'),
    state: yup.string().required('State is required'),
    stateCode: yup.string(),
    zip: yup.string().trim().required('Zip is required')
  }).required('Company address is required'),
  phoneNumber: yup.string().test('phone-digits', 'Phone number must have at least 10 digits', function(value) {
    if (!value) return true; // Let required validation handle empty values
    const digitCount = (value.match(/\d/g) || []).length;
    return digitCount >= 10;
  }).required('Phone number is required'),
});

const ReminderYouAreAlmostTherePopup = ({ isReminderPopup, open, close, dialogRef }) => {
  const { userData , referenceData} = useGlobalStore();
  const setIsRequiredSellerSettingsFilled = useGlobalStore(state => state.setIsRequiredSellerSettingsFilled);

  const {
    handleSubmit,
    control,
    setFocus,
    watch,
    setValue,
    getValues,
    register,
    setError,
    clearErrors,
    trigger,
    reset,
    formState: { errors, isSubmitting, isDirty },
  } = useForm({
    resolver: yupResolver(schema),
    shouldFocusError: false,
  });

  console.log(errors)
  const [states, setStates] = useState([]);
  const [showSucessPopup, setShowSucessPopup] = useState(false);
  const [errorKeys, setErrorKeys] = useImmer([]);
  const isButtonDisabled =  isSubmitting || !isDirty;
  const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
  const { data: companyListsData } = useGetCompanyLists(open);
  const { mutateAsync: saveUserSettings , isLoading: isSellerSettingLoading , data: saveSellerSettingData } = useSaveUserSettings();
  const {setShowLoader} = useGlobalStore();
  const addressContainerRef = useRef<HTMLDivElement>(null);
  const line1InputRef = useRef<HTMLInputElement>(null);
  const [isInputFocused, setIsInputFocused] = useState<any>({
    companyAddress: false,
    phoneNumber: false,
});
const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
const {sellerSettings} = useSellerSettingStore();
const [isFadeLoaderOpen, setIsFadeLoaderOpen] = useState(false);
const [fadeLoaderMessage, setFadeLoaderMessage] = useState('');


  useEffect(() => {
    if(sellerSettings) {
        setValue('companyAddress', {
          line1: sellerSettings?.company_address?.line1 || '',
          line2: sellerSettings?.company_address?.line2 || '',
          city: sellerSettings?.company_address?.city || '',
          state: sellerSettings?.company_address?.state_id || '',
          stateCode: sellerSettings?.company_address?.state_code || '',
          zip: sellerSettings?.company_address?.zip || '',
        });
        setValue(
          'phoneNumber',
          sellerSettings?.phone
            ? formatPhoneNumber(
                formatPhoneNumberRemovingCountryCode(sellerSettings?.phone)
              )
            : ''
        );    
      }
  }, []);


  useEffect(() => {
    if (saveSellerSettingData && !isSellerSettingLoading) {
      setShowSucessPopup(true);
      setShowLoader(false);
    }
  }, [isSellerSettingLoading, saveSellerSettingData]);

  useEffect(() => {
    if(isFadeLoaderOpen){
      setFadeLoaderMessage('Saving...');
    }else{
      if(fadeLoaderMessage === 'Saving...'){
        setFadeLoaderMessage('Saved');
      }
    }
  }, [isFadeLoaderOpen]);


  useEffect(() => {
    if(referenceData?.ref_states) {
        setStates(referenceData?.ref_states)
    }
  }, [referenceData])

  
  useEffect(() => {
    if (isAddressContainerClicked && line1InputRef.current) {
      // Small delay to ensure the input is rendered
      setTimeout(() => {
        line1InputRef.current?.focus();
      }, 0);
    }
  }, [isAddressContainerClicked]);

    useEffect(() => {
      handleStateZipValidation('companyAddress.zip', 'companyAddress.state')
  }, [watch('companyAddress.state'), watch('companyAddress.zip')])



  // Custom clickaway handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!isAddressContainerClicked) return;

      const target = event.target as HTMLElement;

      // Check if click is inside the address container
      if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
        return;
      }

      // Check if click is inside any state selector
      const stateSelectorElement = document.querySelector('[data-state-selector]');
      if (stateSelectorElement && stateSelectorElement.contains(target)) {
        return;
      }

      // If we get here, the click was outside both the container and state selector
      handleShipmentAddressContainerClickAway();
    };

    // Add event listener when address container is clicked
    if (isAddressContainerClicked) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAddressContainerClicked]);


  useEffect(() => {
    if (errors?.companyAddress) {
      setIsAddressContainerClicked(true)
    }
  }, [errors])

  const handleStateZipValidation = async (zipCode, stateCode) => {
    if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
      const payload = {
        data: {
          state_id: Number(getValues(stateCode)),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
      } else {
        setError(
          stateCode,
          { message: "The zip code and state code do not match" },
          { shouldFocus: true }
        );
        setError(
          zipCode,
          { message: "The zip code and state code do not match" },
          { shouldFocus: true }
        );
      }
    }
  };
  
  const handleInputFocus = (inputName: any): void => {
    setIsInputFocused((prevState) => ({
        ...prevState,
        [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: any): void => {
      setIsInputFocused((prevState) => ({
          ...prevState,
          [inputName]: false,
      }));
  };

  
  const handleShipmentAddressContainerClickAway = () => {
    handleInputBlur('companyAddress')
    if (!isStateSelectorFocused) {
      if(!(errors?.companyAddress?.line1 || errors?.companyAddress?.line2 || errors?.companyAddress?.city || errors?.companyAddress?.state || errors?.companyAddress?.zip || errors?.companyAddress?.stateCode)){
        setIsAddressContainerClicked(false)
      }else{
        setIsAddressContainerClicked(true)
      }
    }
  }

  const handleSave = async (data) => {
    try {
      setIsFadeLoaderOpen(true);
      const companyPayload = {
        company_name: sellerSettings.company_name,
        company_address: {
          line1: data.companyAddress.line1,
          city: data.companyAddress.city,
          state_id: Number(data.companyAddress.state),
          zip: data.companyAddress.zip,
        },
        send_invoices_to: SEND_INVOICES_TO,
        shipping_docs_to: SHIPPING_DOCS_TO,
      }

      const userSettingsPayload = {
        first_name: sellerSettings.first_name,
        last_name: sellerSettings.last_name,
        email_id: sellerSettings.email_id,
        phone: data.phoneNumber
      }
      let companyResponse = await saveUserSettings({ route: 'user/seller/settings/company', data: companyPayload })
        if (companyResponse) {
          let userResponse = await saveUserSettings({ route: 'user/seller/settings', data: userSettingsPayload })
          setIsRequiredSellerSettingsFilled(true)
          setIsFadeLoaderOpen(false);
          setTimeout(() => {
            close()
          }, 500)
      }
    } catch (error) {
      console.log("error @>>>>>>>", error)
      setIsFadeLoaderOpen(false);
    } 
  }


  return (
    <Dialog
      open={open}
      transitionDuration={200}
      hideBackdrop
      container={dialogRef.current}
      style={{
        position: 'absolute',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backdropFilter: 'blur(7px)',
        WebkitBackdropFilter: 'blur(7px)',
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        borderRadius: '0px 0px 16px 16px'
      }}
      classes={{
        root: styles.reminderPopup,
        paper: styles.dialogContent,
        container:styles.containerPopup
      }}
    >
      <div className={styles.reminderPopupContainer}>
      <span
                className={styles.closeIcon}
                onClick={close}
              >
                <CloseIcon />
              </span>
          <div className={styles.reminderPopupTitle}>
            <p className={styles.titleText}>
              REMINDER: TELL US WHO YOU ARE
            </p>
            <p className={styles.titleSmallText}>
              We need to know a little bit about you before you can begin
              accepting orders. Please provide the following information.{" "}
            </p>
          </div>
        <div>
          {/* COMPANY HQ ADDRESS */}
          <div className={clsx(styles.formGroupInput, styles.companyHQAddressContainer)}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.companyAddress && styles.focusLbl)} htmlFor="companyAddress">
                Company Address
              </label>
            </span>
            <span className={clsx(styles.col1, styles.locationAddressContainer)}>
              {
                (isAddressContainerClicked || errors?.companyAddress) ? (
                  <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                    <span className={clsx(styles.addresInputMain)}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line1 && styles.error)}
                          type='text'
                          register={register('companyAddress.line1')}
                          placeholder='Address 1'
                          onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                            register('companyAddress.line1').onBlur(e);
                            handleInputBlur('companyAddress')
                          }}
                          onFocus={() => handleInputFocus('companyAddress')}
                          errorInput={errors?.companyAddress?.line1}
                          inputRef={(e: any) => {
                            line1InputRef.current = e;
                          }}
                        />
                      </InputWrapper>
                    </span>

                    <span className={clsx(styles.addresInputMain)}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line2 && styles.error)}
                          type='text'
                          // autoFocus={true}
                          register={register('companyAddress.line2')}
                          placeholder='Address 2'
                          onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                            register('companyAddress.line2').onBlur(e);
                            handleInputBlur('companyAddress')
                          }}
                          onFocus={() => handleInputFocus('companyAddress')}
                          errorInput={errors?.companyAddress?.line2}
                        />
                      </InputWrapper>

                    </span>


                    <span className={styles.zipInputContainer}>
                      <span className={clsx(styles.col1, styles.addresInputMain)}>
                        <InputWrapper>
                          <CustomTextField
                            className={clsx(styles.inputCreateAccount, errors?.companyAddress?.city && styles.error)}
                            type='text'
                            register={register('companyAddress.city')}
                            placeholder='City'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                              register('companyAddress.city').onBlur(e);
                              handleInputBlur('companyAddress')
                            }}
                            onFocus={() => handleInputFocus('companyAddress')}
                            errorInput={errors?.companyAddress?.city}
                          />
                        </InputWrapper>
                      </span>
                      <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                        <Controller
                          name="companyAddress.state"
                          control={control}
                          render={({ field }) => (
                            <>
                              <SingleStateSelector
                                states={states.map((state: any) => ({ state_code: state.code }))}
                                value={field.value}
                                onChange={(stateCode) => {
                                  const selectedState = states.find((state: any) => state.code === stateCode);
                                  if (selectedState) {
                                    field.onChange(selectedState.id);
                                    setValue('companyAddress.stateCode', selectedState.code);
                                    // Clear any exis ting errors for the state field
                                    if (errors?.companyAddress?.state) {
                                      clearErrors('companyAddress.state');
                                    }
                                    // Trigger validation after setting the value
                                    setTimeout(() => {
                                      trigger('companyAddress.state');
                                    }, 0);
                                  } else {
                                    console.error('State not found for code:', stateCode);
                                  }
                                }}
                                onBlur={() => {
                                  field.onBlur();
                                  handleInputBlur('companyAddress');
                                }}
                                error={!!errors?.companyAddress?.state}
                                placeholder="State"
                                stateIdToCode={(stateId) => {
                                  const state = states.find((s: any) => s.id === stateId);
                                  return state ? state.code : watch('companyAddress.stateCode');
                                }}
                                onFocus={() => handleInputFocus('companyAddress')}
                              />

                            </>
                          )}
                        />

                      </span>
                      <span className={clsx(styles.col3, styles.addresInputMain)}>
                        <InputWrapper>
                          <CustomTextField
                            className={clsx(styles.inputCreateAccount, (errors?.companyAddress?.zip || errors?.companyAddress?.state) && styles.error)}
                            type='text'
                            maxLength={5}
                            register={register('companyAddress.zip')}
                            placeholder='Zip Code'
                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                              register('companyAddress.zip').onBlur(e);
                              handleInputBlur('companyAddress');
                            }}
                            onFocus={() => handleInputFocus('companyAddress')}
                            errorInput={errors?.companyAddress?.zip || errors?.companyAddress?.state}
                            mode="wholeNumber"
                            onKeyDown={(e) => {
                              if (e.key === 'Tab') {
                                if (!e.shiftKey) {
                                  handleShipmentAddressContainerClickAway()
                                }
                              }
                            }}
                          />
                        </InputWrapper>
                      </span>
                    </span>

                  </div>
                ) : (
                  <div className={styles.addressDisplayContainer} onClick={() => setIsAddressContainerClicked(true)}>
                    {
                      (watch('companyAddress.line1') || watch('companyAddress.line2') || watch('companyAddress.city') || watch('companyAddress.state') || watch('companyAddress.zip')) ? (
                        <div className={styles.valueDiv}>
                          <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line1') ? `${watch('companyAddress.line1')}` : ''}</p>
                          <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line2') ? `${watch('companyAddress.line2')}` : ''}</p>
                          <span className={styles.lastAddressFiled}>
                            <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('companyAddress.city') ? `${watch('companyAddress.city')}` : ''}</p>
                            <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{watch('companyAddress.stateCode') ? `${watch('companyAddress.stateCode')}` : ''}</p>
                            <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('companyAddress.zip') ? `${watch('companyAddress.zip')}` : ''}</p>
                          </span>
                        </div>
                      ) : (
                        <span className={clsx(styles.valueDiv, styles.placeHolderDiv)} onClick={() => setIsAddressContainerClicked(true)}></span>
                      )
                    }
                  </div>
                )
              }
            </span>
          </div>

                  {/* YOUR PHONE NUMBER */}
        <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.phoneNumber && styles.focusLbl)}
              htmlFor='phoneNumber'
            >
              YOUR PHONE NUMBER
            </label>
          </span>
          <span className={styles.col1}>
          <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.phoneNumber && styles.error
                )}
                type='tel'
                register={register('phoneNumber')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('phoneNumber').onBlur(e);
                  handleInputBlur('phoneNumber');
                }}
                onFocus={() => handleInputFocus('phoneNumber')}
                errorInput={errors?.phoneNumber}
                mode='phoneNumber'
                lastFocusable="user-tab"
                onKeyDown={(e) => {
                  if (e.key === 'Tab') {
                    if(!e.shiftKey){
                      e.preventDefault();
                    const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                    if (saveButton) {
                      if (saveButton.disabled) {
                        const companyButton = document.getElementById('COMPANY')
                        if (companyButton) {
                          (companyButton as HTMLElement).focus();
                        }
                      } else {
                        setTimeout(() => {
                          saveButton.focus();
                        }, 0);
                      }
                    }
                  }
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>
        <div className={styles.reminderPopupNote}>
          <p>
          Please note you will also need to upload your <span style={{fontWeight: 'bold'}}>W-9 and complete the banking info</span> to receive
          payment on this order.
          </p>
        </div>
        <div className={styles.saveButtonContainer}>
          <span className={clsx(styles.fadeLoaderMessage, fadeLoaderMessage === 'Saved' && styles.fadeLoaderMessageSaved)}>{fadeLoaderMessage}</span>
          <button className={styles.saveButton} disabled={isButtonDisabled} onClick={() => handleSubmit(handleSave)()}> Save</button>
        </div>

        </div>
      </div>
    </Dialog>
  );
};

export default ReminderYouAreAlmostTherePopup;
